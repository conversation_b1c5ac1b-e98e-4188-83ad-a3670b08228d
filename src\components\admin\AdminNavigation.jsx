import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaHome, 
  FaStore, 
  FaChartBar, 
  FaCreditCard, 
  FaCog, 
  FaUsers,
  FaQrcode,
  FaEdit
} from 'react-icons/fa';

const AdminNavigation = () => {
  const location = useLocation();

  const navItems = [
    {
      path: '/admin/dashboard',
      icon: FaHome,
      label: 'Dashboard',
      description: 'Overview & KPIs'
    },
    {
      path: '/admin/restaurants',
      icon: FaStore,
      label: 'Restaurants',
      description: 'Manage all restaurants'
    },
    {
      path: '/admin/analytics',
      icon: FaChartBar,
      label: 'Analytics',
      description: 'Reports & insights'
    },
    {
      path: '/admin/billing',
      icon: FaCreditCard,
      label: 'Billing',
      description: 'Subscriptions & payments'
    },
    {
      path: '/admin/settings',
      icon: FaCog,
      label: 'Settings',
      description: 'Platform configuration'
    }
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 mb-8">
      <h2 className="text-xl font-fredoka text-white mb-6">Admin Navigation</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className="block"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 rounded-xl transition-all duration-300 ${
                isActive(item.path)
                  ? 'bg-accent text-white shadow-lg'
                  : 'bg-white/5 text-white/80 hover:bg-white/10 hover:text-white'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <item.icon className="text-xl" />
                <span className="font-raleway font-semibold">{item.label}</span>
              </div>
              <p className={`text-sm font-raleway ${
                isActive(item.path) ? 'text-white/90' : 'text-white/60'
              }`}>
                {item.description}
              </p>
            </motion.div>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-6 border-t border-white/10">
        <h3 className="text-lg font-fredoka text-white mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Link
            to="/admin/restaurants"
            className="bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 px-4 py-2 rounded-lg font-raleway text-sm transition-colors flex items-center space-x-2"
          >
            <FaStore />
            <span>Add Restaurant</span>
          </Link>
          <Link
            to="/admin/analytics"
            className="bg-green-600/20 hover:bg-green-600/30 text-green-400 px-4 py-2 rounded-lg font-raleway text-sm transition-colors flex items-center space-x-2"
          >
            <FaChartBar />
            <span>View Reports</span>
          </Link>
          <Link
            to="/admin/settings"
            className="bg-purple-600/20 hover:bg-purple-600/30 text-purple-400 px-4 py-2 rounded-lg font-raleway text-sm transition-colors flex items-center space-x-2"
          >
            <FaUsers />
            <span>Manage Users</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminNavigation;
