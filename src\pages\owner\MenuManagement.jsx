import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaImage } from 'react-icons/fa';

const MenuManagement = () => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('categories');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('category'); // 'category' or 'item'
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    image: null
  });

  const categories = [
    { id: 1, name: 'Appetizers', items: 8 },
    { id: 2, name: 'Main Course', items: 15 },
    { id: 3, name: 'Desserts', items: 6 },
    { id: 4, name: 'Beverages', items: 12 }
  ];

  const menuItems = [
    { id: 1, name: 'Caesar Salad', category: 'Appetizers', price: '$12.99', status: 'available' },
    { id: 2, name: 'Grilled Chicken', category: 'Main Course', price: '$24.99', status: 'available' },
    { id: 3, name: 'Chocolate Cake', category: 'Desserts', price: '$8.99', status: 'out-of-stock' },
    { id: 4, name: 'Fresh Juice', category: 'Beverages', price: '$5.99', status: 'available' }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    setShowModal(false);
    setFormData({ name: '', description: '', price: '', category: '', image: null });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const openModal = (type) => {
    setModalType(type);
    setShowModal(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-fredoka text-white mb-2">Menu Management</h1>
          <p className="text-white/70 font-raleway">Manage your restaurant's menu categories and items</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4">
        <button
          onClick={() => setActiveTab('categories')}
          className={`px-6 py-3 rounded-lg font-raleway font-semibold transition-colors ${
            activeTab === 'categories'
              ? 'bg-accent text-white'
              : 'bg-white/10 text-white/70 hover:text-white'
          }`}
        >
          Categories
        </button>
        <button
          onClick={() => setActiveTab('items')}
          className={`px-6 py-3 rounded-lg font-raleway font-semibold transition-colors ${
            activeTab === 'items'
              ? 'bg-accent text-white'
              : 'bg-white/10 text-white/70 hover:text-white'
          }`}
        >
          Menu Items
        </button>
      </div>

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <div className="space-y-6">
          <div className="flex justify-end">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => openModal('category')}
              className="bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 px-6 rounded-lg flex items-center space-x-2"
            >
              <FaPlus />
              <span>Add Category</span>
            </motion.button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-fredoka text-white">{category.name}</h3>
                  <div className="flex space-x-2">
                    <button className="text-yellow-400 hover:text-yellow-300">
                      <FaEdit />
                    </button>
                    <button className="text-red-400 hover:text-red-300">
                      <FaTrash />
                    </button>
                  </div>
                </div>
                <p className="text-white/70 font-raleway">
                  {category.items} items
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Menu Items Tab */}
      {activeTab === 'items' && (
        <div className="space-y-6">
          <div className="flex justify-end">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => openModal('item')}
              className="bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 px-6 rounded-lg flex items-center space-x-2"
            >
              <FaPlus />
              <span>Add Menu Item</span>
            </motion.button>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/10 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="px-6 py-4 text-left text-white font-raleway font-semibold">Name</th>
                    <th className="px-6 py-4 text-left text-white font-raleway font-semibold">Category</th>
                    <th className="px-6 py-4 text-left text-white font-raleway font-semibold">Price</th>
                    <th className="px-6 py-4 text-left text-white font-raleway font-semibold">Status</th>
                    <th className="px-6 py-4 text-left text-white font-raleway font-semibold">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {menuItems.map((item) => (
                    <tr key={item.id} className="border-t border-white/10">
                      <td className="px-6 py-4 text-white font-raleway">{item.name}</td>
                      <td className="px-6 py-4 text-white/70 font-raleway">{item.category}</td>
                      <td className="px-6 py-4 text-white font-raleway font-semibold">{item.price}</td>
                      <td className="px-6 py-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-raleway ${
                          item.status === 'available' 
                            ? 'bg-green-500/20 text-green-400' 
                            : 'bg-red-500/20 text-red-400'
                        }`}>
                          {item.status === 'available' ? 'Available' : 'Out of Stock'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex space-x-2">
                          <button className="text-yellow-400 hover:text-yellow-300">
                            <FaEdit />
                          </button>
                          <button className="text-red-400 hover:text-red-300">
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-primary border border-white/20 rounded-xl p-6 w-full max-w-md mx-4"
          >
            <h2 className="text-2xl font-fredoka text-white mb-6">
              Add New {modalType === 'category' ? 'Category' : 'Menu Item'}
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <input
                type="text"
                name="name"
                placeholder={modalType === 'category' ? 'Category Name' : 'Item Name'}
                value={formData.name}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <textarea
                name="description"
                placeholder="Description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent resize-none"
              />
              
              {modalType === 'item' && (
                <>
                  <input
                    type="number"
                    name="price"
                    placeholder="Price"
                    value={formData.price}
                    onChange={handleChange}
                    step="0.01"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                    required
                  />
                  
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-accent"
                    required
                  >
                    <option value="">Select Category</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.name} className="bg-primary">
                        {cat.name}
                      </option>
                    ))}
                  </select>
                  
                  <div className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center">
                    <FaImage className="w-8 h-8 text-white/40 mx-auto mb-2" />
                    <p className="text-white/60 font-raleway text-sm">
                      Click to upload image or drag and drop
                    </p>
                    <input type="file" className="hidden" accept="image/*" />
                  </div>
                </>
              )}
              
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-white/10 hover:bg-white/20 text-white font-raleway py-3 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-accent hover:bg-accent/90 text-white font-raleway py-3 rounded-lg transition-colors"
                >
                  Add {modalType === 'category' ? 'Category' : 'Item'}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default MenuManagement;