import React from 'react';
import { motion } from 'framer-motion';

const Settings = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">Settings</h1>
        <p className="text-white/80 font-raleway">Manage your restaurant settings</p>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">Settings Coming Soon</h3>
          <p className="text-white/60 font-raleway">
            Restaurant settings and configuration options will be available here.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default Settings;
