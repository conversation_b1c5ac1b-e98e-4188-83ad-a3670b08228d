import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaCopy } from 'react-icons/fa';

const SimpleAdminDashboard = () => {
  // Load restaurant owners from localStorage or use defaults
  const loadOwners = () => {
    const stored = localStorage.getItem('restaurantOwners');
    return stored ? JSON.parse(stored) : [
      {
        id: 1,
        email: '<EMAIL>',
        password: 'demo123',
        restaurantName: 'Demo Restaurant',
        ownerName: 'John Demo',
        status: 'active',
        createdAt: '2024-01-15'
      },
      {
        id: 2,
        email: '<EMAIL>',
        password: 'bella456',
        restaurantName: 'Bella Vista',
        ownerName: 'Maria Bella',
        status: 'active',
        createdAt: '2024-01-10'
      }
    ];
  };

  const [restaurantOwners, setRestaurantOwners] = useState(loadOwners);

  // Save to localStorage whenever owners change
  const updateOwners = (newOwners) => {
    setRestaurantOwners(newOwners);
    localStorage.setItem('restaurantOwners', JSON.stringify(newOwners));
  };

  const [showForm, setShowForm] = useState(false);
  const [editingOwner, setEditingOwner] = useState(null);
  const [showPasswords, setShowPasswords] = useState({});
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    restaurantName: '',
    ownerName: ''
  });

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (editingOwner) {
      // Update existing owner
      const updatedOwners = restaurantOwners.map(owner =>
        owner.id === editingOwner.id
          ? { ...owner, ...formData }
          : owner
      );
      updateOwners(updatedOwners);
    } else {
      // Create new owner
      const newOwner = {
        id: Date.now(),
        ...formData,
        status: 'active',
        createdAt: new Date().toISOString().split('T')[0]
      };
      updateOwners([...restaurantOwners, newOwner]);
    }

    // Reset form
    setFormData({ email: '', password: '', restaurantName: '', ownerName: '' });
    setShowForm(false);
    setEditingOwner(null);
  };

  const handleEdit = (owner) => {
    setFormData({
      email: owner.email,
      password: owner.password,
      restaurantName: owner.restaurantName,
      ownerName: owner.ownerName
    });
    setEditingOwner(owner);
    setShowForm(true);
  };

  const handleDelete = (id) => {
    if (confirm('Are you sure you want to delete this restaurant owner?')) {
      const updatedOwners = restaurantOwners.filter(owner => owner.id !== id);
      updateOwners(updatedOwners);
    }
  };

  const togglePasswordVisibility = (id) => {
    setShowPasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  const copyCredentials = (owner) => {
    const credentials = `Email: ${owner.email}\nPassword: ${owner.password}\nRestaurant: ${owner.restaurantName}`;
    navigator.clipboard.writeText(credentials);
    alert('Credentials copied to clipboard!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-fredoka text-white mb-2">Restaurant Owner Management</h1>
            <p className="text-white/70 font-raleway">Create and manage restaurant owner accounts</p>
          </div>
          <motion.button
            onClick={() => {
              setShowForm(true);
              setEditingOwner(null);
              setFormData({ email: '', password: generatePassword(), restaurantName: '', ownerName: '' });
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-accent hover:bg-accent/90 text-white px-6 py-3 rounded-lg font-raleway font-semibold flex items-center space-x-2"
          >
            <FaPlus />
            <span>Add Restaurant Owner</span>
          </motion.button>
        </div>

        {/* Form Modal */}
        {showForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-fredoka text-white mb-4">
                {editingOwner ? 'Edit Restaurant Owner' : 'Add Restaurant Owner'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-white/80 font-raleway mb-2">Owner Name</label>
                  <input
                    type="text"
                    value={formData.ownerName}
                    onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}
                    className="w-full bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-white/80 font-raleway mb-2">Restaurant Name</label>
                  <input
                    type="text"
                    value={formData.restaurantName}
                    onChange={(e) => setFormData({ ...formData, restaurantName: e.target.value })}
                    className="w-full bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-white/80 font-raleway mb-2">Email</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-white/80 font-raleway mb-2">Password</label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      className="flex-1 bg-white/5 border border-white/20 rounded-lg py-3 px-4 text-white"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setFormData({ ...formData, password: generatePassword() })}
                      className="bg-white/10 hover:bg-white/20 text-white px-4 py-3 rounded-lg"
                    >
                      Generate
                    </button>
                  </div>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-accent hover:bg-accent/90 text-white py-3 rounded-lg font-raleway font-semibold"
                  >
                    {editingOwner ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingOwner(null);
                    }}
                    className="flex-1 bg-white/10 hover:bg-white/20 text-white py-3 rounded-lg font-raleway font-semibold"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}

        {/* Restaurant Owners Table */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="text-left p-4 text-white font-raleway">Owner</th>
                  <th className="text-left p-4 text-white font-raleway">Restaurant</th>
                  <th className="text-left p-4 text-white font-raleway">Email</th>
                  <th className="text-left p-4 text-white font-raleway">Password</th>
                  <th className="text-left p-4 text-white font-raleway">Status</th>
                  <th className="text-left p-4 text-white font-raleway">Actions</th>
                </tr>
              </thead>
              <tbody>
                {restaurantOwners.map((owner) => (
                  <tr key={owner.id} className="border-t border-white/10">
                    <td className="p-4 text-white font-raleway">{owner.ownerName}</td>
                    <td className="p-4 text-white font-raleway">{owner.restaurantName}</td>
                    <td className="p-4 text-white/80 font-raleway">{owner.email}</td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-white/80 font-raleway">
                          {showPasswords[owner.id] ? owner.password : '••••••••'}
                        </span>
                        <button
                          onClick={() => togglePasswordVisibility(owner.id)}
                          className="text-white/60 hover:text-white"
                        >
                          {showPasswords[owner.id] ? <FaEyeSlash /> : <FaEye />}
                        </button>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-raleway">
                        {owner.status}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => copyCredentials(owner)}
                          className="text-blue-400 hover:text-blue-300"
                          title="Copy credentials"
                        >
                          <FaCopy />
                        </button>
                        <button
                          onClick={() => handleEdit(owner)}
                          className="text-yellow-400 hover:text-yellow-300"
                          title="Edit"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDelete(owner.id)}
                          className="text-red-400 hover:text-red-300"
                          title="Delete"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-6 bg-white/5 rounded-lg p-4">
          <h4 className="text-white font-raleway font-semibold mb-2">Instructions:</h4>
          <ul className="text-white/70 font-raleway text-sm space-y-1">
            <li>• Create restaurant owner accounts with unique credentials</li>
            <li>• Share credentials securely with restaurant owners</li>
            <li>• Restaurant owners use these credentials to access their dashboard</li>
            <li>• Use the copy button to easily share credentials</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SimpleAdminDashboard;
