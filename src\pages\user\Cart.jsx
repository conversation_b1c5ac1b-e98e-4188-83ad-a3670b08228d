import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { updateQuantity, removeItem } from '../../store/slices/cartSlice';
import { FaPlus, FaMinus, FaTrash } from 'react-icons/fa';

const Cart = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { restaurantId, tableNumber } = useParams();
  const { items, total } = useSelector((state) => state.cart);

  const handleQuantityChange = (itemId, newQuantity) => {
    dispatch(updateQuantity({ itemId, quantity: newQuantity }));
  };

  const handleRemoveItem = (itemId) => {
    dispatch(removeItem(itemId));
  };

  const proceedToPayment = () => {
    navigate(`/restaurant/${restaurantId}/table/${tableNumber}/payment`);
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <div className="text-center">
          <h2 className="text-2xl font-fredoka text-white mb-4">Your cart is empty</h2>