import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  FaStore, 
  FaShoppingCart, 
  FaDollarSign, 
  FaUsers,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa';

const AdminDashboard = () => {
  const dispatch = useDispatch();
  const { restaurants } = useSelector((state) => state.restaurant);

  const kpiCards = [
    {
      title: 'Total Restaurants',
      value: '24',
      change: '+12%',
      trend: 'up',
      icon: FaStore,
      color: 'bg-blue-500'
    },
    {
      title: 'Active Orders',
      value: '156',
      change: '+8%',
      trend: 'up',
      icon: FaShoppingCart,
      color: 'bg-green-500'
    },
    {
      title: 'Monthly Revenue',
      value: '$12,450',
      change: '+15%',
      trend: 'up',
      icon: FaDollarSign,
      color: 'bg-accent'
    },
    {
      title: 'Active Users',
      value: '1,234',
      change: '-2%',
      trend: 'down',
      icon: FaUsers,
      color: 'bg-purple-500'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-fredoka text-white mb-2">
            Admin Dashboard
          </h1>
          <p className="text-white/70 font-raleway">
            Welcome back! Here's what's happening with TableServe today.
          </p>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${card.color}`}>
                <card.icon className="w-6 h-6 text-white" />
              </div>
              <div className={`flex items-center text-sm font-raleway ${
                card.trend === 'up' ? 'text-green-400' : 'text-red-400'
              }`}>
                {card.trend === 'up' ? <FaArrowUp /> : <FaArrowDown />}
                <span className="ml-1">{card.change}</span>
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-white mb-1">
              {card.value}
            </h3>
            <p className="text-white/70 font-raleway text-sm">
              {card.title}
            </p>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-fredoka text-white mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 px-6 rounded-lg transition-colors">
            Add New Restaurant
          </button>
          <button className="bg-white/10 hover:bg-white/20 text-white font-raleway font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20">
            View Analytics
          </button>
          <button className="bg-white/10 hover:bg-white/20 text-white font-raleway font-semibold py-3 px-6 rounded-lg transition-colors border border-white/20">
            Manage Billing
          </button>
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-fredoka text-white mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((item) => (
            <div key={item} className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0">
              <div>
                <p className="text-white font-raleway">New restaurant "Spice Garden" added</p>
                <p className="text-white/60 text-sm">2 hours ago</p>
              </div>
              <span className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-raleway">
                New
              </span>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default AdminDashboard;