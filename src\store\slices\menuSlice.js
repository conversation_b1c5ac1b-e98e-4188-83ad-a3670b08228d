import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { menuAPI } from '../../services/api';

export const fetchMenuByRestaurant = createAsyncThunk(
  'menu/fetchMenuByRestaurant',
  async (restaurantId, { rejectWithValue }) => {
    try {
      const response = await menuAPI.getByRestaurant(restaurantId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

export const createMenuItem = createAsyncThunk(
  'menu/createMenuItem',
  async ({ restaurantId, categoryId, itemData }, { rejectWithValue }) => {
    try {
      const response = await menuAPI.createItem(restaurantId, categoryId, itemData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

const menuSlice = createSlice({
  name: 'menu',
  initialState: {
    categories: [],
    items: [],
    loading: false,
    error: null,
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateItemAvailability: (state, action) => {
      const { itemId, available } = action.payload;
      const item = state.items.find(item => item.id === itemId);
      if (item) {
        item.available = available;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMenuByRestaurant.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchMenuByRestaurant.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.categories;
        state.items = action.payload.items;
      })
      .addCase(fetchMenuByRestaurant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, updateItemAvailability } = menuSlice.actions;
export default menuSlice.reducer;