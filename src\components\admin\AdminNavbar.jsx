import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { logoutUser } from '../../store/slices/authSlice';
import { FaBars, Fa<PERSON>ell, FaUser, FaSignOutAlt } from 'react-icons/fa';

const AdminNavbar = ({ sidebarOpen, setSidebarOpen }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/login');
  };

  return (
    <motion.nav
      initial={{ y: -50 }}
      animate={{ y: 0 }}
      className="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-lg border-b border-white/10 z-50"
    >
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-white hover:text-accent transition-colors"
          >
            <FaBars className="w-5 h-5" />
          </button>
          <h1 className="text-xl font-fredoka text-accent">TableServe Admin</h1>
        </div>

        <div className="flex items-center space-x-4">
          <button className="relative text-white hover:text-accent transition-colors">
            <FaBell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              3
            </span>
          </button>

          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-white font-raleway text-sm">{user?.name || 'Admin'}</p>
              <p className="text-white/60 text-xs">{user?.role}</p>
            </div>
            <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center">
              <FaUser className="w-4 h-4 text-white" />
            </div>
          </div>

          <button
            onClick={handleLogout}
            className="text-white hover:text-red-400 transition-colors"
          >
            <FaSignOutAlt className="w-5 h-5" />
          </button>
        </div>
      </div>
    </motion.nav>
  );
};

export default AdminNavbar;