import React from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaShoppingCart, FaTable } from 'react-icons/fa';

const UserNavbar = ({ restaurantId, tableNumber }) => {
  const navigate = useNavigate();
  const { items } = useSelector((state) => state.cart);
  const cartItemCount = items.reduce((total, item) => total + item.quantity, 0);

  return (
    <motion.nav
      initial={{ y: -50 }}
      animate={{ y: 0 }}
      className="fixed top-0 left-0 right-0 bg-white/10 backdrop-blur-lg border-b border-white/10 z-50"
    >
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-fredoka text-accent">TableServe</h1>
          <div className="flex items-center space-x-2 text-white/70">
            <FaTable className="w-4 h-4" />
            <span className="font-raleway text-sm">Table {tableNumber}</span>
          </div>
        </div>

        <button
          onClick={() => navigate(`/restaurant/${restaurantId}/table/${tableNumber}/cart`)}
          className="relative bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <FaShoppingCart className="w-4 h-4" />
          <span className="font-raleway">Cart</span>
          {cartItemCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {cartItemCount}
            </span>
          )}
        </button>
      </div>
    </motion.nav>
  );
};

export default UserNavbar;