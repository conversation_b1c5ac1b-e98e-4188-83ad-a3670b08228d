import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import { useSelector } from 'react-redux';

// Import all route components
import AdminRoutes from './routes/AdminRoutes';
import OwnerRoutes from './routes/OwnerRoutes';
import UserRoutes from './routes/UserRoutes';
import Login from './components/auth/Login';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Import individual page components
import AdminDashboard from './pages/admin/AdminDashboard';
import RestaurantManagement from './pages/admin/RestaurantManagement';
import MenuManagement from './pages/admin/MenuManagement';
import Analytics from './pages/admin/Analytics';
import BillingManagement from './pages/admin/BillingManagement';
import UserManagement from './pages/admin/UserManagement';
import Settings from './pages/admin/Settings';

import OwnerDashboard from './pages/owner/OwnerDashboard';
import OwnerMenuManagement from './pages/owner/MenuManagement';
import OrderManagement from './pages/owner/OrderManagement';
import TableManagement from './pages/owner/TableManagement';
import OwnerAnalytics from './pages/owner/Analytics';
import OwnerSettings from './pages/owner/Settings';

import MenuView from './pages/user/MenuView';
import Cart from './pages/user/Cart';
import Payment from './pages/user/Payment';
import OrderTracking from './pages/user/OrderTracking';
import Feedback from './pages/user/Feedback';

// Marketing website components (existing)
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';

import AOS from 'aos';
import { useEffect } from 'react';
import 'aos/dist/aos.css';

function AppContent() {
  const { isAuthenticated, user } = useSelector((state) => state.auth);

  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Marketing Website Routes */}
        <Route path="/" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* Authentication Routes */}
        <Route path="/login" element={<Login />} />

        {/* Admin Panel Routes */}
        <Route path="/admin" element={
          <ProtectedRoute requiredRole="admin">
            <AdminRoutes />
          </ProtectedRoute>
        }>
          <Route index element={<AdminDashboard />} />
          <Route path="restaurants" element={<RestaurantManagement />} />
          <Route path="menus" element={<MenuManagement />} />
          <Route path="analytics" element={<Analytics />} />
          <Route path="billing" element={<BillingManagement />} />
          <Route path="users" element={<UserManagement />} />
          <Route path="settings" element={<Settings />} />
        </Route>

        {/* Restaurant Owner Dashboard Routes */}
        <Route path="/owner" element={
          <ProtectedRoute requiredRole="owner">
            <OwnerRoutes />
          </ProtectedRoute>
        }>
          <Route index element={<OwnerDashboard />} />
          <Route path="menu" element={<OwnerMenuManagement />} />
          <Route path="orders" element={<OrderManagement />} />
          <Route path="tables" element={<TableManagement />} />
          <Route path="analytics" element={<OwnerAnalytics />} />
          <Route path="settings" element={<OwnerSettings />} />
        </Route>

        {/* User QR-based Routes */}
        <Route path="/restaurant/:restaurantId/table/:tableNumber" element={<UserRoutes />}>
          <Route index element={<MenuView />} />
          <Route path="cart" element={<Cart />} />
          <Route path="payment" element={<Payment />} />
          <Route path="order/:orderId" element={<OrderTracking />} />
          <Route path="feedback" element={<Feedback />} />
        </Route>

        {/* Redirect based on user role */}
        <Route path="/dashboard" element={
          isAuthenticated ? (
            user?.role === 'admin' ? <Navigate to="/admin" /> :
              user?.role === 'owner' ? <Navigate to="/owner" /> :
                <Navigate to="/" />
          ) : <Navigate to="/login" />
        } />
      </Routes>
    </Router>
  );
}

export default function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

