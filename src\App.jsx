import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import simple components that don't use Redux for now
import SimpleLogin from './components/auth/SimpleLogin';
import SimpleMenuView from './components/user/SimpleMenuView';
import QRDemo from './components/QRDemo';
import SimpleAdminDashboard from './components/admin/SimpleAdminDashboard';
import SimpleOwnerDashboard from './components/owner/SimpleOwnerDashboard';

// Marketing website components (existing)
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';

import AOS from 'aos';
import { useEffect } from 'react';
import 'aos/dist/aos.css';

function AppContent() {
  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Marketing Website Routes */}
        <Route path="/" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* QR Demo Route */}
        <Route path="/qr-demo" element={<QRDemo />} />

        {/* Admin Routes - tableserve/admin/... */}
        <Route path="/admin/login" element={<SimpleLogin userType="admin" />} />
        <Route path="/admin/dashboard" element={<SimpleAdminDashboard />} />
        <Route path="/admin/restaurants" element={<div className="p-8 text-white">Restaurant Management</div>} />
        <Route path="/admin/restaurants/:restaurantId/edit" element={<div className="p-8 text-white">Edit Restaurant</div>} />
        <Route path="/admin/analytics" element={<div className="p-8 text-white">Admin Analytics</div>} />
        <Route path="/admin/billing" element={<div className="p-8 text-white">Billing Management</div>} />
        <Route path="/admin/settings" element={<div className="p-8 text-white">Admin Settings</div>} />

        {/* Restaurant Owner Routes - tableserve/:restaurantName/... */}
        <Route path="/:restaurantName/login" element={<SimpleLogin userType="owner" />} />
        <Route path="/:restaurantName/dashboard" element={<SimpleOwnerDashboard />} />
        <Route path="/:restaurantName/orders" element={<div className="p-8 text-white">Order Management</div>} />
        <Route path="/:restaurantName/menu" element={<div className="p-8 text-white">Menu Management</div>} />
        <Route path="/:restaurantName/tables" element={<div className="p-8 text-white">Table Management</div>} />
        <Route path="/:restaurantName/analytics" element={<div className="p-8 text-white">Restaurant Analytics</div>} />
        <Route path="/:restaurantName/settings" element={<div className="p-8 text-white">Restaurant Settings</div>} />

        {/* User/Diner Routes - tableserve/:restaurantName/:tableId/... */}
        <Route path="/:restaurantName/:tableId/menu" element={<SimpleMenuView />} />
        <Route path="/:restaurantName/:tableId/cart" element={<div className="p-8 text-white">Cart</div>} />
        <Route path="/:restaurantName/:tableId/payment" element={<div className="p-8 text-white">Payment</div>} />
        <Route path="/:restaurantName/:tableId/order-tracking" element={<div className="p-8 text-white">Order Tracking</div>} />
        <Route path="/:restaurantName/:tableId/feedback" element={<div className="p-8 text-white">Feedback</div>} />

        {/* Legacy redirects */}
        <Route path="/login" element={<Navigate to="/admin/login" />} />
        <Route path="/admin" element={<Navigate to="/admin/dashboard" />} />
        <Route path="/owner" element={<Navigate to="/admin/login" />} />
      </Routes>
    </Router>
  );
}

export default function App() {
  return <AppContent />;
}

