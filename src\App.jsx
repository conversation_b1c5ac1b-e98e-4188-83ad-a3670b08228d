import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import simple components that don't use Redux for now
import SimpleLogin from './components/auth/SimpleLogin';
import SimpleMenuView from './components/user/SimpleMenuView';
import QRDemo from './components/QRDemo';

// Marketing website components (existing)
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';

import AOS from 'aos';
import { useEffect } from 'react';
import 'aos/dist/aos.css';

function AppContent() {
  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Marketing Website Routes */}
        <Route path="/" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* Authentication Routes */}
        <Route path="/login" element={<SimpleLogin />} />

        {/* QR Demo Route */}
        <Route path="/qr-demo" element={<QRDemo />} />

        {/* Temporary simplified routes - Redux will be added back later */}
        <Route path="/admin" element={<div className="p-8 text-white">Admin Dashboard (Redux integration coming soon)</div>} />
        <Route path="/owner" element={<div className="p-8 text-white">Owner Dashboard (Redux integration coming soon)</div>} />
        <Route path="/restaurant/:restaurantId/table/:tableNumber" element={<SimpleMenuView />} />

        {/* Simple dashboard redirect - Redux integration coming soon */}
        <Route path="/dashboard" element={<Navigate to="/login" />} />
      </Routes>
    </Router>
  );
}

export default function App() {
  return <AppContent />;
}

