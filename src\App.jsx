import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import simple components that don't use Redux for now
import SimpleLogin from './components/auth/SimpleLogin';

// Marketing website components (existing)
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';

import AOS from 'aos';
import { useEffect } from 'react';
import 'aos/dist/aos.css';

function AppContent() {
  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Marketing Website Routes */}
        <Route path="/" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* Authentication Routes */}
        <Route path="/login" element={<SimpleLogin />} />

        {/* Temporary simplified routes - Redux will be added back later */}
        <Route path="/admin" element={<div className="p-8 text-white">Admin Dashboard (Redux integration coming soon)</div>} />
        <Route path="/owner" element={<div className="p-8 text-white">Owner Dashboard (Redux integration coming soon)</div>} />
        <Route path="/restaurant/:restaurantId/table/:tableNumber" element={<div className="p-8 text-white">QR Menu (Redux integration coming soon)</div>} />

        {/* Redirect based on user role */}
        <Route path="/dashboard" element={
          isAuthenticated ? (
            user?.role === 'admin' ? <Navigate to="/admin" /> :
              user?.role === 'owner' ? <Navigate to="/owner" /> :
                <Navigate to="/" />
          ) : <Navigate to="/login" />
        } />
      </Routes>
    </Router>
  );
}

export default function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

