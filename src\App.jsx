import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import { useSelector } from 'react-redux';

// Import all route components
import AdminRoutes from './routes/AdminRoutes';
import OwnerRoutes from './routes/OwnerRoutes';
import UserRoutes from './routes/UserRoutes';
import Login from './components/auth/Login';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Marketing website components (existing)
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import Layout from './components/Layout';
import ScrollToTop from './components/ScrollToTop';

import AOS from 'aos';
import { useEffect } from 'react';
import 'aos/dist/aos.css';

function AppContent() {
  const { isAuthenticated, user } = useSelector((state) => state.auth);

  useEffect(() => {
    AOS.init({ duration: 1000 });
  }, []);

  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Marketing Website Routes */}
        <Route path="/" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* Authentication Routes */}
        <Route path="/login" element={<Login />} />

        {/* Admin Panel Routes */}
        <Route path="/admin/*" element={
          <ProtectedRoute requiredRole="admin">
            <AdminRoutes />
          </ProtectedRoute>
        } />

        {/* Restaurant Owner Dashboard Routes */}
        <Route path="/owner/*" element={
          <ProtectedRoute requiredRole="owner">
            <OwnerRoutes />
          </ProtectedRoute>
        } />

        {/* User QR-based Routes */}
        <Route path="/restaurant/:restaurantId/table/:tableNumber/*" element={<UserRoutes />} />

        {/* Redirect based on user role */}
        <Route path="/dashboard" element={
          isAuthenticated ? (
            user?.role === 'admin' ? <Navigate to="/admin" /> :
            user?.role === 'owner' ? <Navigate to="/owner" /> :
            <Navigate to="/" />
          ) : <Navigate to="/login" />
        } />
      </Routes>
    </Router>
  );
}

export default function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

