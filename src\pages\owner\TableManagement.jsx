import React from 'react';
import { motion } from 'framer-motion';

const TableManagement = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">Table Management</h1>
        <p className="text-white/80 font-raleway">Manage your restaurant tables and QR codes</p>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">Table Management Coming Soon</h3>
          <p className="text-white/60 font-raleway">
            This feature will allow you to manage your restaurant tables and generate QR codes for each table.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default TableManagement;
