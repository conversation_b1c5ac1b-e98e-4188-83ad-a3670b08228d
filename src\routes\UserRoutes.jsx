import React from 'react';
import { Routes, Route } from 'react-router-dom';
import UserLayout from '../components/user/UserLayout';
import MenuView from '../pages/user/MenuView';
import Cart from '../pages/user/Cart';
import Payment from '../pages/user/Payment';
import OrderTracking from '../pages/user/OrderTracking';
import Feedback from '../pages/user/Feedback';

const UserRoutes = () => {
  return (
    <UserLayout>
      <Routes>
        <Route index element={<MenuView />} />
        <Route path="cart" element={<Cart />} />
        <Route path="payment" element={<Payment />} />
        <Route path="order/:orderId" element={<OrderTracking />} />
        <Route path="feedback" element={<Feedback />} />
      </Routes>
    </UserLayout>
  );
};

export default UserRoutes;