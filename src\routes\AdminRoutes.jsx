import React from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminLayout from '../components/admin/AdminLayout';
import AdminDashboard from '../pages/admin/AdminDashboard';
import RestaurantManagement from '../pages/admin/RestaurantManagement';
import MenuManagement from '../pages/admin/MenuManagement';
import Analytics from '../pages/admin/Analytics';
import BillingManagement from '../pages/admin/BillingManagement';
import UserManagement from '../pages/admin/UserManagement';
import Settings from '../pages/admin/Settings';

const AdminRoutes = () => {
  return (
    <AdminLayout>
      <Routes>
        <Route index element={<AdminDashboard />} />
        <Route path="restaurants" element={<RestaurantManagement />} />
        <Route path="menus" element={<MenuManagement />} />
        <Route path="analytics" element={<Analytics />} />
        <Route path="billing" element={<BillingManagement />} />
        <Route path="users" element={<UserManagement />} />
        <Route path="settings" element={<Settings />} />
      </Routes>
    </AdminLayout>
  );
};

export default AdminRoutes;