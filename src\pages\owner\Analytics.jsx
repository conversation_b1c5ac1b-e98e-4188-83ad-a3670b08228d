import React from 'react';
import { motion } from 'framer-motion';

const Analytics = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">Analytics</h1>
        <p className="text-white/80 font-raleway">Track your restaurant performance</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Total Orders</h3>
          <p className="text-3xl font-fredoka text-accent">0</p>
        </div>
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Revenue</h3>
          <p className="text-3xl font-fredoka text-accent">$0</p>
        </div>
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Avg. Order Value</h3>
          <p className="text-3xl font-fredoka text-accent">$0</p>
        </div>
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Popular Items</h3>
          <p className="text-3xl font-fredoka text-accent">0</p>
        </div>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">Detailed Analytics Coming Soon</h3>
          <p className="text-white/60 font-raleway">
            Advanced analytics and reporting features will be available here.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default Analytics;
