import React from 'react';
import { motion } from 'framer-motion';

const OrderManagement = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">Order Management</h1>
        <p className="text-white/80 font-raleway">Manage incoming orders and track their status</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Pending Orders</h3>
          <p className="text-3xl font-fredoka text-accent">0</p>
        </div>
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">In Progress</h3>
          <p className="text-3xl font-fredoka text-accent">0</p>
        </div>
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
          <h3 className="text-lg font-raleway text-white mb-2">Completed Today</h3>
          <p className="text-3xl font-fredoka text-accent">0</p>
        </div>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">No Orders Yet</h3>
          <p className="text-white/60 font-raleway">
            Orders from customers will appear here once they start placing orders.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default OrderManagement;
