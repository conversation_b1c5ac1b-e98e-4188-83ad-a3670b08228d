import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchRestaurants, createRestaurant } from '../../store/slices/restaurantSlice';
import { FaPlus, FaEdit, FaTrash, FaQrcode, FaEye } from 'react-icons/fa';

const RestaurantManagement = () => {
  const dispatch = useDispatch();
  const { restaurants, loading } = useSelector((state) => state.restaurant);
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    tableCount: 10,
    description: ''
  });

  useEffect(() => {
    dispatch(fetchRestaurants());
  }, [dispatch]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    await dispatch(createRestaurant(formData));
    setShowModal(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      tableCount: 10,
      description: ''
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-fredoka text-white mb-2">Restaurant Management</h1>
          <p className="text-white/70 font-raleway">Manage all restaurants on the platform</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowModal(true)}
          className="bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 px-6 rounded-lg flex items-center space-x-2"
        >
          <FaPlus />
          <span>Add Restaurant</span>
        </motion.button>
      </div>

      {/* Restaurant Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {restaurants.map((restaurant, index) => (
          <motion.div
            key={restaurant.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-fredoka text-white">{restaurant.name}</h3>
              <div className="flex space-x-2">
                <button className="text-blue-400 hover:text-blue-300">
                  <FaEye />
                </button>
                <button className="text-yellow-400 hover:text-yellow-300">
                  <FaEdit />
                </button>
                <button className="text-red-400 hover:text-red-300">
                  <FaTrash />
                </button>
              </div>
            </div>
            
            <div className="space-y-2 mb-4">
              <p className="text-white/70 font-raleway text-sm">
                <span className="font-semibold">Email:</span> {restaurant.email}
              </p>
              <p className="text-white/70 font-raleway text-sm">
                <span className="font-semibold">Tables:</span> {restaurant.tableCount}
              </p>
              <p className="text-white/70 font-raleway text-sm">
                <span className="font-semibold">Status:</span> 
                <span className="ml-2 px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs">
                  Active
                </span>
              </p>
            </div>

            <button className="w-full bg-white/10 hover:bg-white/20 text-white font-raleway py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors">
              <FaQrcode />
              <span>Generate QR Codes</span>
            </button>
          </motion.div>
        ))}
      </div>

      {/* Add Restaurant Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-primary border border-white/20 rounded-xl p-6 w-full max-w-md mx-4"
          >
            <h2 className="text-2xl font-fredoka text-white mb-6">Add New Restaurant</h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <input
                type="text"
                name="name"
                placeholder="Restaurant Name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <input
                type="email"
                name="email"
                placeholder="Owner Email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <input
                type="tel"
                name="phone"
                placeholder="Phone Number"
                value={formData.phone}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <input
                type="text"
                name="address"
                placeholder="Address"
                value={formData.address}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <input
                type="number"
                name="tableCount"
                placeholder="Number of Tables"
                value={formData.tableCount}
                onChange={handleChange}
                min="1"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent"
                required
              />
              
              <textarea
                name="description"
                placeholder="Restaurant Description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-accent resize-none"
              />
              
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-white/10 hover:bg-white/20 text-white font-raleway py-3 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-accent hover:bg-accent/90 text-white font-raleway py-3 rounded-lg transition-colors"
                >
                  Add Restaurant
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default RestaurantManagement;