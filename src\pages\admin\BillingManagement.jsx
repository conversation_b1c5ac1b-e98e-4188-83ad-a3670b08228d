import React from 'react';
import { motion } from 'framer-motion';

const BillingManagement = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">Billing Management</h1>
        <p className="text-white/80 font-raleway">Manage subscriptions and billing for restaurants</p>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">Billing Management Coming Soon</h3>
          <p className="text-white/60 font-raleway">
            This feature will allow you to manage billing and subscriptions for all restaurants.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default BillingManagement;
