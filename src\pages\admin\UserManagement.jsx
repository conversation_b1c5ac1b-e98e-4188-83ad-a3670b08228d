import React from 'react';
import { motion } from 'framer-motion';

const UserManagement = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6"
    >
      <div className="mb-6">
        <h1 className="text-3xl font-fredoka text-white mb-2">User Management</h1>
        <p className="text-white/80 font-raleway">Manage restaurant owners and admin users</p>
      </div>

      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-raleway text-white mb-4">User Management Coming Soon</h3>
          <p className="text-white/60 font-raleway">
            This feature will allow you to manage all users on the platform.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default UserManagement;
