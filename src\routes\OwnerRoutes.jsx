import React from 'react';
import { Routes, Route } from 'react-router-dom';
import OwnerLayout from '../components/owner/OwnerLayout';
import OwnerDashboard from '../pages/owner/OwnerDashboard';
import MenuManagement from '../pages/owner/MenuManagement';
import OrderManagement from '../pages/owner/OrderManagement';
import TableManagement from '../pages/owner/TableManagement';
import Analytics from '../pages/owner/Analytics';
import Settings from '../pages/owner/Settings';

const OwnerRoutes = () => {
  return (
    <OwnerLayout>
      <Routes>
        <Route index element={<OwnerDashboard />} />
        <Route path="menu" element={<MenuManagement />} />
        <Route path="orders" element={<OrderManagement />} />
        <Route path="tables" element={<TableManagement />} />
        <Route path="analytics" element={<Analytics />} />
        <Route path="settings" element={<Settings />} />
      </Routes>
    </OwnerLayout>
  );
};

export default OwnerRoutes;