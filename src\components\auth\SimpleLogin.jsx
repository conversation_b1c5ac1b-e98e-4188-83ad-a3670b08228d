import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaUser, FaLock, FaArrowRight } from 'react-icons/fa';

const SimpleLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState('admin');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const navigate = useNavigate();

  // Log demo credentials to console for development
  React.useEffect(() => {
    console.log('=== TableServe Demo Credentials ===');
    console.log('Admin Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123');
    console.log('');
    console.log('Restaurant Owner Login:');
    console.log('  Credentials are managed by admin');
    console.log('  Default demo: <EMAIL> / demo123');
    console.log('  Login as admin to create/manage owner accounts');
    console.log('');
    console.log('Customer Access: Scan QR code at restaurant table');
    console.log('=====================================');
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Authentication logic
    setTimeout(() => {
      setLoading(false);

      if (role === 'admin') {
        // Built-in admin credentials
        if (email === '<EMAIL>' && password === 'admin123') {
          navigate('/admin');
        } else {
          setError('Invalid admin credentials. Please check your email and password.');
        }
      } else if (role === 'owner') {
        // Get restaurant owners from localStorage (managed by admin)
        const storedOwners = localStorage.getItem('restaurantOwners');
        const validOwners = storedOwners ? JSON.parse(storedOwners) : [
          // Default demo owners if none exist
          { email: '<EMAIL>', password: 'demo123', restaurantName: 'Demo Restaurant', ownerName: 'John Demo' },
          { email: '<EMAIL>', password: 'bella456', restaurantName: 'Bella Vista', ownerName: 'Maria Bella' }
        ];

        const validOwner = validOwners.find(owner =>
          owner.email === email && owner.password === password && owner.status !== 'inactive'
        );

        if (validOwner) {
          // Store current owner info for the session
          sessionStorage.setItem('currentOwner', JSON.stringify(validOwner));
          navigate('/owner');
        } else {
          setError('Invalid restaurant owner credentials. Please contact admin for access.');
        }
      } else {
        setError('Users access the app by scanning QR codes at restaurant tables.');
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-gray-900 to-black">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        <div className="text-center mb-8">
          <h1 className="text-4xl font-fredoka text-white mb-2">TableServe Login</h1>
          <p className="text-white/60 font-raleway">Admin & Restaurant Owner Access</p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-xl">
          {error && (
            <div className="bg-red-500/20 text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-white/80 font-raleway mb-2">Email</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaUser className="text-white/40" />
                </div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg py-3 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-white/80 font-raleway mb-2">Password</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-white/40" />
                </div>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg py-3 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                  placeholder="Enter your password"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-white/80 font-raleway mb-2">Login As</label>
              <select
                value={role}
                onChange={(e) => setRole(e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg py-3 px-4 text-white focus:outline-none focus:border-accent"
              >
                <option value="admin" className="bg-gray-800">Admin</option>
                <option value="owner" className="bg-gray-800">Restaurant Owner</option>
              </select>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 accent-accent bg-white/5 border-white/10 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-white/60 font-raleway">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className="font-raleway text-accent hover:text-accent/80">
                  Forgot password?
                </a>
              </div>
            </div>

            <motion.button
              type="submit"
              disabled={loading}
              whileHover={{ scale: loading ? 1 : 1.02 }}
              whileTap={{ scale: loading ? 1 : 0.98 }}
              className={`w-full flex justify-center items-center space-x-2 bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 rounded-lg transition-colors ${loading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
            >
              <span>Sign In</span>
              {loading ? (
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <FaArrowRight />
              )}
            </motion.button>
          </form>
        </div>

        {/* Customer Access Info */}
        <div className="mt-6 text-center">
          <p className="text-white/60 font-raleway text-sm">
            <strong>Customers:</strong> Access restaurant menus by scanning QR codes at your table.
          </p>
          <p className="text-white/40 font-raleway text-xs mt-2">
            Check browser console for demo credentials during development.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleLogin;
