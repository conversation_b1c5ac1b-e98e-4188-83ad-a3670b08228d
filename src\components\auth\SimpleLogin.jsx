import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaUser, FaLock, FaArrowRight } from 'react-icons/fa';

const SimpleLogin = ({ userType }) => {
  const { restaurantName } = useParams();
  const [mobile, setMobile] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [loginMethod, setLoginMethod] = useState('password'); // 'password' or 'otp'
  const [otpSent, setOtpSent] = useState(false);

  // Determine role based on userType prop or URL
  const role = userType || (restaurantName ? 'owner' : 'admin');

  const navigate = useNavigate();

  const sendOTP = () => {
    if (!mobile) {
      setError('Please enter mobile number');
      return;
    }

    setLoading(true);
    setError('');

    // Simulate OTP sending
    setTimeout(() => {
      setLoading(false);
      setOtpSent(true);
      setError('');
      console.log('OTP sent to:', mobile, '- Demo OTP: 123456');
    }, 1500);
  };

  // Log demo credentials to console for development
  React.useEffect(() => {
    console.log('=== TableServe Demo Credentials ===');
    console.log('Admin Login:');
    console.log('  Mobile: +**********');
    console.log('  Password: admin123');
    console.log('  OTP: 123456 (for OTP login)');
    console.log('');
    console.log('Restaurant Owner Login:');
    console.log('  Credentials are managed by admin');
    console.log('  Default demo: +********** / demo123');
    console.log('  Login as admin to create/manage owner accounts');
    console.log('');
    console.log('Customer Access: Scan QR code at restaurant table');
    console.log('=====================================');
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Authentication logic
    setTimeout(() => {
      setLoading(false);

      if (role === 'admin') {
        // Built-in admin credentials
        const isValidPassword = mobile === '+**********' && password === 'admin123';
        const isValidOTP = mobile === '+**********' && loginMethod === 'otp' && otp === '123456';

        if (isValidPassword || isValidOTP) {
          navigate('/admin/dashboard');
        } else {
          setError('Invalid admin credentials. Please check your mobile number and password/OTP.');
        }
      } else if (role === 'owner') {
        // Get restaurant owners from localStorage (managed by admin)
        const storedOwners = localStorage.getItem('restaurantOwners');
        const validOwners = storedOwners ? JSON.parse(storedOwners) : [
          // Default demo owners if none exist
          { mobile: '+**********', password: 'demo123', restaurantName: 'Demo Restaurant', ownerName: 'John Demo' },
          { mobile: '+9876543211', password: 'bella456', restaurantName: 'Bella Vista', ownerName: 'Maria Bella' }
        ];

        let validOwner = null;

        if (loginMethod === 'password') {
          validOwner = validOwners.find(owner =>
            owner.mobile === mobile && owner.password === password && owner.status !== 'inactive'
          );
        } else if (loginMethod === 'otp' && otp === '123456') {
          validOwner = validOwners.find(owner =>
            owner.mobile === mobile && owner.status !== 'inactive'
          );
        }

        if (validOwner) {
          // Store current owner info for the session
          sessionStorage.setItem('currentOwner', JSON.stringify(validOwner));
          // Navigate to restaurant-specific dashboard
          const restaurantSlug = restaurantName || validOwner.restaurantName.toLowerCase().replace(/\s+/g, '-');
          navigate(`/${restaurantSlug}/dashboard`);
        } else {
          setError('Invalid restaurant owner credentials. Please contact admin for access.');
        }
      } else {
        setError('Users access the app by scanning QR codes at restaurant tables.');
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-gray-900 to-black">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md"
      >
        <div className="text-center mb-8">
          <h1 className="text-4xl font-fredoka text-white mb-2">
            {role === 'admin' ? 'Admin Login' : `${restaurantName || 'Restaurant'} Login`}
          </h1>
          <p className="text-white/60 font-raleway">
            {role === 'admin' ? 'Platform Administration' : 'Restaurant Management Access'}
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-xl">
          {error && (
            <div className="bg-red-500/20 text-red-300 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {/* Login Method Toggle */}
          <div className="mb-6">
            <div className="flex bg-white/5 rounded-lg p-1">
              <button
                type="button"
                onClick={() => {
                  setLoginMethod('password');
                  setOtpSent(false);
                  setOtp('');
                }}
                className={`flex-1 py-2 px-4 rounded-md font-raleway text-sm transition-colors ${loginMethod === 'password'
                  ? 'bg-accent text-white'
                  : 'text-white/70 hover:text-white'
                  }`}
              >
                Password Login
              </button>
              <button
                type="button"
                onClick={() => {
                  setLoginMethod('otp');
                  setPassword('');
                }}
                className={`flex-1 py-2 px-4 rounded-md font-raleway text-sm transition-colors ${loginMethod === 'otp'
                  ? 'bg-accent text-white'
                  : 'text-white/70 hover:text-white'
                  }`}
              >
                OTP Login
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-white/80 font-raleway mb-2">Mobile Number</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaUser className="text-white/40" />
                </div>
                <input
                  type="tel"
                  value={mobile}
                  onChange={(e) => setMobile(e.target.value)}
                  className="w-full bg-white/5 border border-white/10 rounded-lg py-3 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                  placeholder="Enter mobile number (+**********)"
                  required
                />
              </div>
            </div>

            {/* Password Field - only show for password login */}
            {loginMethod === 'password' && (
              <div>
                <label className="block text-white/80 font-raleway mb-2">Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="text-white/40" />
                  </div>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full bg-white/5 border border-white/10 rounded-lg py-3 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                    placeholder="Enter your password"
                    required
                  />
                </div>
              </div>
            )}

            {/* OTP Section - only show for OTP login */}
            {loginMethod === 'otp' && (
              <div>
                <label className="block text-white/80 font-raleway mb-2">OTP</label>
                {!otpSent ? (
                  <button
                    type="button"
                    onClick={sendOTP}
                    disabled={loading}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-raleway font-semibold transition-colors disabled:opacity-50"
                  >
                    {loading ? 'Sending OTP...' : 'Send OTP'}
                  </button>
                ) : (
                  <div>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaLock className="text-white/40" />
                      </div>
                      <input
                        type="text"
                        value={otp}
                        onChange={(e) => setOtp(e.target.value)}
                        className="w-full bg-white/5 border border-white/10 rounded-lg py-3 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none focus:border-accent"
                        placeholder="Enter 6-digit OTP"
                        maxLength="6"
                        required
                      />
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <p className="text-white/60 text-sm font-raleway">
                        OTP sent to {mobile}
                      </p>
                      <button
                        type="button"
                        onClick={sendOTP}
                        className="text-accent hover:text-accent/80 text-sm font-raleway"
                      >
                        Resend OTP
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Role is now determined by URL - no need for selection */}
            <div className="bg-white/5 rounded-lg p-3 text-center">
              <p className="text-white/70 font-raleway text-sm">
                Logging in as: <span className="text-accent font-semibold">
                  {role === 'admin' ? 'Platform Admin' : `${restaurantName || 'Restaurant'} Owner`}
                </span>
              </p>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 accent-accent bg-white/5 border-white/10 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-white/60 font-raleway">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className="font-raleway text-accent hover:text-accent/80">
                  Forgot password?
                </a>
              </div>
            </div>

            <motion.button
              type="submit"
              disabled={loading || (loginMethod === 'otp' && !otpSent)}
              whileHover={{ scale: loading ? 1 : 1.02 }}
              whileTap={{ scale: loading ? 1 : 0.98 }}
              className={`w-full flex justify-center items-center space-x-2 bg-accent hover:bg-accent/90 text-white font-raleway font-semibold py-3 rounded-lg transition-colors ${loading || (loginMethod === 'otp' && !otpSent) ? 'opacity-70 cursor-not-allowed' : ''
                }`}
            >
              <span>
                {loginMethod === 'password' ? 'Sign In' : 'Verify OTP'}
              </span>
              {loading ? (
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <FaArrowRight />
              )}
            </motion.button>
          </form>
        </div>

        {/* Customer Access Info */}
        <div className="mt-6 text-center">
          <p className="text-white/60 font-raleway text-sm">
            <strong>Customers:</strong> Access restaurant menus by scanning QR codes at your table.
          </p>
          <p className="text-white/40 font-raleway text-xs mt-2">
            Check browser console for demo credentials during development.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleLogin;
